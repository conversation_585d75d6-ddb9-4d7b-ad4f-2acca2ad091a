# 用户选择功能测试说明

## 功能概述
已成功将原来的消息对话框拆分为两个模态框：
1. **消息对话框** - 用于输入消息内容和基本信息
2. **选择用户对话框** - 用于筛选和选择目标用户

## 主要修改内容

### 1. 消息对话框修改
- 移除了推送范围相关的字段（属性选择、单位选择）
- 简化为只显示"选择用户"按钮和已选用户显示
- 保留消息类型、内容等核心字段

### 2. 新增选择用户对话框
- 包含推送范围筛选（属性/单位）
- 属性筛选：行业、部门、属地下拉选择
- 单位筛选：单位名称下拉选择
- 用户列表：可多选的用户列表展示
- 显示用户姓名、单位名称、微信号等信息

### 3. 筛选逻辑实现
- **属性筛选**：根据行业、部门、属地调用 `getCompanyList` 获取公司列表，再根据公司ID调用 `getList` 获取用户
- **单位筛选**：直接根据单位ID调用 `getList` 获取用户
- 支持清空筛选条件，实时更新用户列表

### 4. 用户选择功能
- 支持多选用户（必选至少一个）
- 确认选择后返回消息页面
- 显示已选用户名，逗号分割，超过3个显示省略号
- 支持取消选择，恢复之前的选择状态

## 测试步骤

### 1. 基本功能测试
1. 点击"新建"按钮打开消息对话框
2. 点击"选择用户"按钮，应该打开选择用户对话框
3. 验证推送范围切换功能正常
4. 验证筛选条件变化时用户列表更新

### 2. 属性筛选测试
1. 选择推送范围为"属性"
2. 分别选择行业、部门、属地
3. 验证用户列表根据筛选条件正确更新
4. 测试清空筛选条件的功能

### 3. 单位筛选测试
1. 选择推送范围为"单位"
2. 选择具体单位名称
3. 验证用户列表显示该单位的用户
4. 测试清空筛选条件的功能

### 4. 用户选择测试
1. 在用户列表中选择多个用户
2. 点击"确定选择"按钮
3. 验证返回消息对话框并显示已选用户名
4. 测试取消选择功能

### 5. 数据验证测试
1. 不选择用户时点击确定，应该显示验证错误
2. 选择用户后提交表单，验证数据正确传递
3. 编辑已有消息时，验证用户选择状态正确恢复

## API 接口说明

### 使用的接口
- `getCompanyList` - 获取公司列表（用于属性筛选）
- `getList` (from contacts) - 获取用户列表
- `getAllAttList` - 获取属性选项（行业、部门、属地）
- `getAllList` (from company) - 获取所有公司选项

### 参数说明
- **getCompanyList 参数**：
  - `competent`: 部门ID
  - `dependency`: 属地ID  
  - `industry`: 行业ID
  - `pageSize`: 999（获取所有数据）

- **getList 参数**：
  - `unitList`: 公司ID数组，如 [1,2,3]

## 注意事项
1. 确保所有API接口正常可用
2. 验证数据结构与实际接口返回一致
3. 测试各种边界情况（无数据、网络错误等）
4. 确认用户权限控制正常工作
