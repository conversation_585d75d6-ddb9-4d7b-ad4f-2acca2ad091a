<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 160px;">
                    <el-option label="草稿" :value="0" />
                    <el-option label="待审核" :value="1" />
                    <el-option label="已审核" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择类型" clearable style="width: 160px;">
                    <el-option label="文本消息" :value="1" />
                    <el-option label="图文消息" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['message-created']">新建</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="类型" align="center" prop="type" width="100">
                <template #default="scope">
                    <el-tag v-if="scope.row.type == 1" type="primary">文本消息</el-tag>
                    <el-tag v-else-if="scope.row.type == 2" type="success">图文消息</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="消息标题" align="center" prop="title" width="150" show-overflow-tooltip />
            <el-table-column label="封面" align="center" prop="picUrl" width="80">
                <template #default="scope">
                    <el-image v-if="scope.row.picUrl" :src="scope.row.picUrl" style="width: 40px; height: 40px;"
                        fit="cover" />
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="消息内容" align="center" prop="content" show-overflow-tooltip />
            <el-table-column label="链接" align="center" prop="linkUrl" width="150" show-overflow-tooltip />
            <el-table-column label="发送时间" align="center" prop="sendTime" width="150" />
            <el-table-column label="状态" align="center" prop="status" width="100">
                <template #default="scope">
                    <el-tag v-if="scope.row.status == 0" type="warning">草稿</el-tag>
                    <el-tag v-else-if="scope.row.status == 1" type="warning">待审核</el-tag>
                    <el-tag v-else-if="scope.row.status == 2" type="success">已审核</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="320" fixed="right" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="warning" icon="Position" @click="handleSend(scope.row)"
                        v-if="scope.row.status == 0" v-hasPermi="['message-created']">发送</el-button>
                    <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                        v-if="scope.row.status != 2" v-hasPermi="['message-created']">编辑</el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['message-created']">删除</el-button>
                    <el-button link type="success" icon="Check" @click="handleApprove(scope.row)"
                        v-if="scope.row.status == 1" v-hasPermi="['message-checked']">审核</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改消息对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dynamicRules" label-width="120px">
                <el-form-item label="用户" prop="userList">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <el-button type="primary" @click="handleSelectUser" :disabled="isView">选择用户</el-button>
                        <div v-if="selectedUsersDisplay" style="flex: 1; color: #606266;">
                            {{ selectedUsersDisplay }}
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="消息类型" prop="type">
                    <el-radio-group v-model="dataForm.type" :disabled="isView">
                        <el-radio :value="1">文本消息</el-radio>
                        <el-radio :value="2">图文消息</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 文本消息字段 -->
                <div v-if="dataForm.type === 1">
                    <el-form-item label="消息内容" prop="content">
                        <el-input v-model="dataForm.content" type="textarea" :rows="4" placeholder="请输入消息内容"
                            :disabled="isView" />
                    </el-form-item>
                </div>

                <!-- 图文消息字段 -->
                <div v-if="dataForm.type === 2">
                    <el-form-item label="消息标题" prop="title">
                        <el-input v-model="dataForm.title" placeholder="请输入消息标题" :disabled="isView" />
                    </el-form-item>
                    <el-form-item label="封面" prop="picUrl">
                        <el-upload v-if="!isView && !dataForm.picUrl" class="upload-demo" action="#"
                            :auto-upload="false" :show-file-list="false" :on-change="handlePicUpload" accept="image/*">
                            <el-button type="primary">选择图片</el-button>
                        </el-upload>
                        <div v-if="dataForm.picUrl" style="margin-top: 10px;display: flex;align-items: center;">
                            <el-image :src="dataForm.picUrl" style="width: 100px; height: 100px;" fit="cover" />
                            <el-button v-if="!isView" type="danger" size="small" @click="dataForm.picUrl = ''"
                                style="margin-left: 10px;">删除</el-button>
                        </div>
                    </el-form-item>
                    <el-form-item label="消息内容" prop="content">
                        <el-input v-model="dataForm.content" type="textarea" :rows="4" placeholder="请输入消息内容"
                            :disabled="isView" />
                    </el-form-item>
                    <el-form-item label="链接" prop="linkUrl">
                        <el-input v-model="dataForm.linkUrl" placeholder="请输入链接地址" :disabled="isView" />
                    </el-form-item>
                </div>

                <el-form-item label="附件上传" v-if="!isView">
                    <file-upload v-model="dataForm.attachments" :showTip="false" />
                </el-form-item>

                <!-- 查看模式下显示附件 -->
                <el-form-item label="附件" v-if="isView && dataForm.attachments">
                    <file-upload v-model="dataForm.attachments" :disabled="true" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 审核对话框 -->
        <el-dialog title="审核消息" v-model="approveDialogVisible" width="600px" append-to-body>
            <el-form ref="approveForm" :model="approveForm" :rules="dynamicApproveRules" label-width="120px">
                <el-form-item label="审核" prop="status">
                    <el-radio-group v-model="approveForm.status">
                        <el-radio :value="1">通过</el-radio>
                        <el-radio :value="2">不通过</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="审核意见" prop="reviewNote">
                    <el-input v-model="approveForm.reviewNote" type="textarea" :rows="4"
                        :placeholder="approveForm.status === 2 ? '请输入审核意见（必填）' : '请输入审核意见（选填）'" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitApprove">确 定</el-button>
                    <el-button @click="approveDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 选择用户对话框 -->
        <el-dialog title="选择用户" v-model="userSelectDialogVisible" width="900px" append-to-body>
            <div style="margin-bottom: 20px;">
                <el-form :model="userFilterForm" :inline="true" label-width="80px">
                    <el-form-item label="筛选范围">
                        <el-radio-group v-model="userFilterForm.pushScope" @change="handleFilterPushScopeChange">
                            <el-radio value="attribute">属性</el-radio>
                            <el-radio value="company">单位</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>

                <!-- 属性筛选 -->
                <div v-if="userFilterForm.pushScope === 'attribute'" style="margin-top: 15px;">
                    <el-form :model="userFilterForm" :inline="true" label-width="80px">
                        <el-form-item label="行业">
                            <el-select v-model="userFilterForm.industry" placeholder="请选择行业" style="width: 150px;"
                                @change="handleFilterChange" clearable>
                                <el-option v-for="item in industryOptions" :key="item.attId" :label="item.name"
                                    :value="item.attId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="部门">
                            <el-select v-model="userFilterForm.competent" placeholder="请选择部门" style="width: 150px;"
                                @change="handleFilterChange" clearable>
                                <el-option v-for="item in departmentOptions" :key="item.attId" :label="item.name"
                                    :value="item.attId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="属地">
                            <el-select v-model="userFilterForm.dependency" placeholder="请选择属地" style="width: 150px;"
                                @change="handleFilterChange" clearable>
                                <el-option v-for="item in territoryOptions" :key="item.attId" :label="item.name"
                                    :value="item.attId" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 单位筛选 -->
                <div v-if="userFilterForm.pushScope === 'company'" style="margin-top: 15px;">
                    <el-form :model="userFilterForm" :inline="true" label-width="80px">
                        <el-form-item label="单位名称">
                            <el-select v-model="userFilterForm.companyName" placeholder="请选择单位名称" style="width: 200px;"
                                @change="handleFilterChange" clearable>
                                <el-option v-for="item in companyOptions" :key="item.companyId" :label="item.name"
                                    :value="item.companyId" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 调试信息 (开发时使用，生产环境可删除) -->
            <div v-if="userList.length > 0"
                style="margin-bottom: 10px; padding: 8px; background: #f5f7fa; border-radius: 4px; font-size: 12px; color: #666;">
                调试信息：共 {{ userList.length }} 个用户，已选择 {{ selectedUserIds.length }} 个
            </div>

            <!-- 用户列表 -->
            <div style="border: 1px solid #dcdfe6; border-radius: 4px; max-height: 400px; overflow-y: auto;">
                <el-checkbox-group v-model="selectedUserIds" style="width: 100%; display: block;">
                    <div v-for="user in userList" :key="user.wechatId" class="user-item"
                        style="padding: 12px 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; min-height: 60px; cursor: pointer;">
                        <el-checkbox :value="user.wechatId" style="margin-right: 12px; margin-top: 2px;"></el-checkbox>
                        <div style="flex: 1; min-width: 0; overflow: hidden;display: flex;align-items: center;">
                            <div
                                style="margin-right: 8px;font-weight: 500; font-size: 14px; color: #303133;  line-height: 1.4; word-wrap: break-word;">
                                <img :src="user.avatar" style="width: 40px; height: 40px; border-radius: 50%;">
                            </div>
                            <div
                                style="margin-right: 8px;font-weight: 500; font-size: 14px; color: #303133;  line-height: 1.4; word-wrap: break-word;">
                                {{ user.name || '未知用户' }}
                            </div>
                            <div
                                style="font-size: 12px; color: #909399; line-height: 1.3;display: flex;align-items: center;">
                                <div style="word-wrap: break-word;margin-right: 10px;">单位：{{ user.unitName || '-' }}
                                </div>
                                <div style="word-wrap: break-word;">微信号：{{ user.wechatNo || '-' }}</div>
                            </div>
                        </div>
                    </div>
                </el-checkbox-group>
                <div v-if="userList.length === 0" style="text-align: center; padding: 40px; color: #909399;">
                    暂无用户数据
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="confirmSelectUser" :disabled="selectedUserIds.length === 0">
                        确定选择 ({{ selectedUserIds.length }})
                    </el-button>
                    <el-button @click="cancelSelectUser">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getPolicyList, addPolicy, updatePolicy, delPolicy, checkPolicy, sendPolicy } from "@/api/manage/policy";
import { getAllAttList } from "@/api/manage/attApi";
import { getAllList, getCompanyList } from "@/api/manage/company";
import { getList } from "@/api/manage/contacts";

export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            isView: false,
            list: [],
            loading: false,
            queryParams: {
                status: '',
                type: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            approveDialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                userList: [],
                type: 1,
                title: "",
                picUrl: "",
                content: "",
                linkUrl: "",
                attachments: ""
            },
            approveForm: {
                status: "",
                reviewNote: ""
            },
            // 选项数据
            industryOptions: [],
            departmentOptions: [],
            territoryOptions: [],
            companyOptions: [],

            // 选择用户相关
            userSelectDialogVisible: false,
            userList: [],
            selectedUserIds: [],
            selectedUsers: [],
            userFilterForm: {
                pushScope: "",
                industry: "",
                competent: "",
                dependency: "",
                companyName: ""
            }
        }
    },
    computed: {
        // 动态验证规则
        dynamicRules() {
            const rules = {
                userList: [
                    { required: true, message: '请选择用户', trigger: 'change' }
                ],
                type: [
                    { required: true, message: '消息类型不能为空', trigger: 'change' }
                ],
                content: [
                    { required: true, message: '消息内容不能为空', trigger: 'blur' }
                ]
            };

            // 根据消息类型添加相应的验证规则
            if (this.dataForm.type === 2) {
                rules.title = [
                    { required: true, message: '消息标题不能为空', trigger: 'blur' }
                ];
                rules.picUrl = [
                    { required: true, message: '封面不能为空', trigger: 'blur' }
                ];
                rules.linkUrl = [
                    { required: true, message: '链接不能为空', trigger: 'blur' }
                ];
            }

            return rules;
        },

        // 已选用户显示文本
        selectedUsersDisplay() {
            if (this.selectedUsers.length === 0) {
                return '';
            }
            const names = this.selectedUsers.map(user => user.name);
            if (names.length <= 3) {
                return names.join('，');
            } else {
                return names.slice(0, 3).join('，') + '...';
            }
        },
        // 动态审核验证规则
        dynamicApproveRules() {
            const rules = {
                status: [
                    { required: true, message: '审核结果不能为空', trigger: 'change' }
                ]
            };

            // 当审核状态为不通过(2)时，审核意见必填
            if (this.approveForm.status === 2) {
                rules.reviewNote = [
                    { required: true, message: '审核意见不能为空', trigger: 'blur' }
                ];
            }

            return rules;
        }
    },
    mounted() {
        this.getList();
        this.loadOptions();
    },
    methods: {
        // 加载选项数据
        async loadOptions() {
            try {
                // 加载行业选项
                const industryRes = await getAllAttList(1);
                this.industryOptions = industryRes || []

                // 加载部门选项
                const departmentRes = await getAllAttList(2);
                this.departmentOptions = departmentRes || []

                // 加载属地选项
                const territoryRes = await getAllAttList(3);
                this.territoryOptions = territoryRes || []

                // 加载单位选项
                const companyRes = await getAllList();
                this.companyOptions = companyRes || [];
            } catch (error) {
                console.error('加载选项数据失败:', error);
            }
        },

        // 选择用户
        handleSelectUser() {
            // 隐藏消息对话框但不清空数据
            this.dialogVisible = false;
            // 显示用户选择对话框
            this.userSelectDialogVisible = true;
            this.loadUserList();
        },

        // 筛选推送范围变化处理
        handleFilterPushScopeChange() {
            // 清空相关字段
            this.userFilterForm.industry = "";
            this.userFilterForm.competent = "";
            this.userFilterForm.dependency = "";
            this.userFilterForm.companyName = "";
            this.loadUserList();
        },

        // 筛选条件变化处理
        handleFilterChange() {
            this.loadUserList();
        },

        // 加载用户列表
        async loadUserList() {
            try {
                let params = {};

                if (this.userFilterForm.pushScope === 'attribute') {
                    // 属性筛选：先获取公司列表，再获取用户
                    if (this.userFilterForm.industry || this.userFilterForm.competent || this.userFilterForm.dependency) {
                        const companyParams = {
                            competent: this.userFilterForm.competent,
                            dependency: this.userFilterForm.dependency,
                            industry: this.userFilterForm.industry,
                            pageSize: 999
                        };

                        const companyRes = await getCompanyList(companyParams);
                        if (companyRes.code === 200 && companyRes.rows && companyRes.rows.length > 0) {
                            const unitList = companyRes.rows.map(company => company.companyId);
                            params.unitList = unitList;
                        } else {
                            // 没有匹配的公司，清空用户列表
                            this.userList = [];
                            return;
                        }
                    }
                } else if (this.userFilterForm.pushScope === 'company') {
                    // 单位筛选
                    if (this.userFilterForm.companyName) {
                        params.unitList = [this.userFilterForm.companyName];
                    }
                }

                const userRes = await getList(params);
                if (userRes.code === 200) {
                    this.userList = userRes.rows || [];
                    console.log('用户列表数据:', this.userList);
                    console.log('用户数量:', this.userList.length);
                    if (this.userList.length > 0) {
                        console.log('第一个用户数据结构:', this.userList[0]);
                    }
                } else {
                    this.userList = [];
                    console.log('获取用户列表失败:', userRes);
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                this.userList = [];
                this.$message({
                    message: '加载用户列表失败',
                    type: 'error'
                });
            }
        },

        // 确认选择用户
        confirmSelectUser() {
            this.selectedUsers = this.userList.filter(user =>
                this.selectedUserIds.includes(user.wechatId)
            );
            this.dataForm.userList = this.selectedUsers.map(user => user.wechatId);
            this.userSelectDialogVisible = false;
            // 重新显示消息对话框
            this.dialogVisible = true;
        },

        // 取消选择用户
        cancelSelectUser() {
            this.userSelectDialogVisible = false;
            // 恢复之前的选择状态
            this.selectedUserIds = this.selectedUsers.map(user => user.wechatId);
            // 重新显示消息对话框
            this.dialogVisible = true;
        },

        // 图片上传处理
        handlePicUpload(file) {
            // 这里应该调用实际的图片上传接口
            // 暂时使用本地预览
            const reader = new FileReader();
            reader.onload = (e) => {
                this.dataForm.picUrl = e.target.result;
            };
            reader.readAsDataURL(file.raw);
        },

        submitForm() {
            if (this.isView) {
                this.dialogVisible = false;
                return;
            }

            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    try {
                        if (this.isChange) {
                            let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                            dataForm.id = this.editId;
                            let res = await updatePolicy(dataForm);
                            if (res.code === 200) {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                });
                                this.getList();
                                this.dialogVisible = false;
                            }
                        } else {
                            let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                            let res = await addPolicy(dataForm);
                            if (res.code === 200) {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                });
                                this.getList();
                                this.dialogVisible = false;
                            }
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        this.$message({
                            message: '操作失败',
                            type: 'error'
                        });
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.isView = false;
            this.title = "新建消息";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm));
                this.selectedUsers = [];
                this.selectedUserIds = [];
            });
        },

        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm));
            this.selectedUsers = [];
            this.selectedUserIds = [];
            this.userFilterForm = {
                pushScope: "",
                industry: "",
                competent: "",
                dependency: "",
                companyName: ""
            };
            this.dialogVisible = false;
            this.isView = false;
        },

        // 查看
        async handleView(val) {
            this.title = "查看消息";
            this.isView = true;
            this.dataForm = { ...val };
            this.editId = val.id;

            // 如果有用户列表数据，需要设置选中的用户
            if (val.userList && Array.isArray(val.userList)) {
                this.selectedUserIds = val.userList;
                // 这里可能需要根据实际数据结构调整
                this.selectedUsers = val.userList.map(userId => ({
                    wechatId: userId,
                    name: '用户' + userId // 这里应该从实际数据中获取用户名
                }));
            }

            this.dialogVisible = true;
            this.isChange = false;
        },

        // 编辑
        async handleEdit(val) {
            this.title = "修改消息";
            this.isView = false;
            this.dataForm = { ...val };
            this.editId = val.id;

            // 如果有用户列表数据，需要设置选中的用户
            if (val.userList && Array.isArray(val.userList)) {
                this.selectedUserIds = val.userList;
                // 这里可能需要根据实际数据结构调整
                this.selectedUsers = val.userList.map(userId => ({
                    wechatId: userId,
                    name: '用户' + userId // 这里应该从实际数据中获取用户名
                }));
            }

            this.dialogVisible = true;
            this.isChange = true;
        },

        // 删除
        handleDelete(val) {
            this.$confirm('确认删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    let res = await delPolicy({ ids: [val.id] });
                    if (res.code === 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList();
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    this.$message({
                        message: '删除失败',
                        type: 'error'
                    });
                }
            })
        },

        // 审核
        handleApprove(val) {
            this.editId = val.id;
            this.approveForm = {
                status: "",
                reviewNote: ""
            };
            this.approveDialogVisible = true;
        },

        // 提交审核
        submitApprove() {
            this.$refs.approveForm.validate(async (valid) => {
                if (valid) {
                    try {
                        let approveData = {
                            id: this.editId,
                            ...this.approveForm
                        };
                        let res = await checkPolicy(approveData);
                        if (res.code === 200) {
                            this.$message({
                                message: '审核成功',
                                type: 'success'
                            });
                            this.getList();
                            this.approveDialogVisible = false;
                        }
                    } catch (error) {
                        console.error('审核失败:', error);
                        this.$message({
                            message: '审核失败',
                            type: 'error'
                        });
                    }
                }
            });
        },

        // 发送
        handleSend(val) {
            this.$confirm('确认发送该消息吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    let res = await sendPolicy(val.id);
                    if (res.code === 200) {
                        this.$message({
                            message: '发送成功',
                            type: 'success'
                        });
                        this.getList();
                    }
                } catch (error) {
                    console.error('发送失败:', error);
                    this.$message({
                        message: '发送失败',
                        type: 'error'
                    });
                }
            }).catch(() => {
                // 用户取消发送
            });
        },
        // 重置搜索
        reset() {
            this.queryParams = {
                status: '',
                type: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList();
        },

        // 获取列表
        async getList() {
            this.loading = true;
            try {
                let res = await getPolicyList(this.queryParams);
                if (res.code === 200) {
                    this.total = res.total;
                    this.list = res.rows;
                }
            } catch (error) {
                console.error('获取列表失败:', error);
                this.$message({
                    message: '获取列表失败',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}

.user-item {
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #f5f7fa !important;
    }

    &:last-child {
        border-bottom: none;
    }
}
</style>
