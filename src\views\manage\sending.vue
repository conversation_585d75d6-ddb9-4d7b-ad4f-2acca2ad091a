<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 160px;">
                    <el-option label="草稿" :value="0" />
                    <el-option label="待审核" :value="1" />
                    <el-option label="已审核" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择类型" clearable style="width: 160px;">
                    <el-option label="文本消息" :value="1" />
                    <el-option label="图文消息" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['message-created']">新建</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="类型" align="center" prop="type" width="100">
                <template #default="scope">
                    <el-tag v-if="scope.row.type == 1" type="primary">文本消息</el-tag>
                    <el-tag v-else-if="scope.row.type == 2" type="success">图文消息</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="消息标题" align="center" prop="title" width="150" show-overflow-tooltip />
            <el-table-column label="封面" align="center" prop="picUrl" width="80">
                <template #default="scope">
                    <el-image v-if="scope.row.picUrl" :src="scope.row.picUrl" style="width: 40px; height: 40px;"
                        fit="cover" />
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="消息内容" align="center" prop="content" width="200" show-overflow-tooltip />
            <el-table-column label="链接" align="center" prop="linkUrl" width="150" show-overflow-tooltip />
            <el-table-column label="发送时间" align="center" prop="sendTime" width="150" />
            <el-table-column label="状态" align="center" prop="status" width="100">
                <template #default="scope">
                    <el-tag v-if="scope.row.status == 0" type="warning">草稿</el-tag>
                    <el-tag v-else-if="scope.row.status == 1" type="warning">待审核</el-tag>
                    <el-tag v-else-if="scope.row.status == 2" type="success">已审核</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="320" fixed="right" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="warning" icon="Position" @click="handleSend(scope.row)"
                        v-if="scope.row.status == 0" v-hasPermi="['message-created']">发送</el-button>
                    <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                        v-if="scope.row.status != 2" v-hasPermi="['message-created']">编辑</el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['message-created']">删除</el-button>
                    <el-button link type="success" icon="Check" @click="handleApprove(scope.row)"
                        v-if="scope.row.status == 1" v-hasPermi="['message-checked']">审核</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改消息对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dynamicRules" label-width="120px">
                <el-form-item label="用户" prop="userList">
                    <el-button type="primary">选择用户</el-button>
                </el-form-item>
                <el-form-item label="推送范围" prop="pushScope">
                    <el-radio-group v-model="dataForm.pushScope" @change="handlePushScopeChange" :disabled="isView">
                        <el-radio value="attribute">属性</el-radio>
                        <el-radio value="company">单位</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 属性选择 -->
                <div v-if="dataForm.pushScope === 'attribute'">
                    <el-form-item label="行业" prop="industry">
                        <el-select v-model="dataForm.industry" placeholder="请选择行业" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in industryOptions" :key="item.attId" :label="item.name"
                                :value="item.attId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="部门" prop="competent">
                        <el-select v-model="dataForm.competent" placeholder="请选择部门" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in departmentOptions" :key="item.attId" :label="item.name"
                                :value="item.attId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="属地" prop="dependency">
                        <el-select v-model="dataForm.dependency" placeholder="请选择属地" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in territoryOptions" :key="item.attId" :label="item.name"
                                :value="item.attId" />
                        </el-select>
                    </el-form-item>
                </div>

                <!-- 单位选择 -->
                <div v-if="dataForm.pushScope === 'company'">
                    <el-form-item label="单位名称" prop="companyName">
                        <el-select v-model="dataForm.companyName" placeholder="请选择单位名称" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in companyOptions" :key="item.companyId" :label="item.name"
                                :value="item.companyId" />
                        </el-select>
                    </el-form-item>
                </div>

                <el-form-item label="消息类型" prop="type">
                    <el-radio-group v-model="dataForm.type" :disabled="isView">
                        <el-radio :value="1">文本消息</el-radio>
                        <el-radio :value="2">图文消息</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 文本消息字段 -->
                <div v-if="dataForm.type === 1">
                    <el-form-item label="消息内容" prop="content">
                        <el-input v-model="dataForm.content" type="textarea" :rows="4" placeholder="请输入消息内容"
                            :disabled="isView" />
                    </el-form-item>
                </div>

                <!-- 图文消息字段 -->
                <div v-if="dataForm.type === 2">
                    <el-form-item label="消息标题" prop="title">
                        <el-input v-model="dataForm.title" placeholder="请输入消息标题" :disabled="isView" />
                    </el-form-item>
                    <el-form-item label="封面" prop="picUrl">
                        <el-upload v-if="!isView && !dataForm.picUrl" class="upload-demo" action="#"
                            :auto-upload="false" :show-file-list="false" :on-change="handlePicUpload" accept="image/*">
                            <el-button type="primary">选择图片</el-button>
                        </el-upload>
                        <div v-if="dataForm.picUrl" style="margin-top: 10px;display: flex;align-items: center;">
                            <el-image :src="dataForm.picUrl" style="width: 100px; height: 100px;" fit="cover" />
                            <el-button v-if="!isView" type="danger" size="small" @click="dataForm.picUrl = ''"
                                style="margin-left: 10px;">删除</el-button>
                        </div>
                    </el-form-item>
                    <el-form-item label="消息内容" prop="content">
                        <el-input v-model="dataForm.content" type="textarea" :rows="4" placeholder="请输入消息内容"
                            :disabled="isView" />
                    </el-form-item>
                    <el-form-item label="链接" prop="linkUrl">
                        <el-input v-model="dataForm.linkUrl" placeholder="请输入链接地址" :disabled="isView" />
                    </el-form-item>
                </div>

                <el-form-item label="附件上传" v-if="!isView">
                    <file-upload v-model="dataForm.attachments" :showTip="false" />
                </el-form-item>

                <!-- 查看模式下显示附件 -->
                <el-form-item label="附件" v-if="isView && dataForm.attachments">
                    <file-upload v-model="dataForm.attachments" :disabled="true" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 审核对话框 -->
        <el-dialog title="审核消息" v-model="approveDialogVisible" width="600px" append-to-body>
            <el-form ref="approveForm" :model="approveForm" :rules="dynamicApproveRules" label-width="120px">
                <el-form-item label="审核" prop="status">
                    <el-radio-group v-model="approveForm.status">
                        <el-radio :value="1">通过</el-radio>
                        <el-radio :value="2">不通过</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="审核意见" prop="reviewNote">
                    <el-input v-model="approveForm.reviewNote" type="textarea" :rows="4"
                        :placeholder="approveForm.status === 2 ? '请输入审核意见（必填）' : '请输入审核意见（选填）'" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitApprove">确 定</el-button>
                    <el-button @click="approveDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getPolicyList, addPolicy, updatePolicy, delPolicy, checkPolicy, sendPolicy } from "@/api/manage/policy";
import { getAllAttList } from "@/api/manage/attApi";
import { getAllList } from "@/api/manage/company";

export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            isView: false,
            list: [],
            loading: false,
            queryParams: {
                status: '',
                type: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            approveDialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                pushScope: "",
                industry: "",
                competent: "",
                dependency: "",
                companyName: "",
                type: 1,
                title: "",
                picUrl: "",
                content: "",
                linkUrl: "",
                attachments: ""
            },
            approveForm: {
                status: "",
                reviewNote: ""
            },
            // 选项数据
            industryOptions: [],
            departmentOptions: [],
            territoryOptions: [],
            companyOptions: []
        }
    },
    computed: {
        // 动态验证规则
        dynamicRules() {
            const rules = {
                userList: [
                    { required: true, message: '请选择用户', trigger: 'change' }
                ],
                type: [
                    { required: true, message: '消息类型不能为空', trigger: 'change' }
                ],
                content: [
                    { required: true, message: '消息内容不能为空', trigger: 'blur' }
                ]
            };

            // 根据推送范围添加相应的验证规则
            if (this.dataForm.pushScope === 'attribute') {
                rules.industry = [
                    { required: true, message: '行业不能为空', trigger: 'change' }
                ];
                rules.competent = [
                    { required: true, message: '部门不能为空', trigger: 'change' }
                ];
                rules.dependency = [
                    { required: true, message: '属地不能为空', trigger: 'change' }
                ];
            } else if (this.dataForm.pushScope === 'company') {
                rules.companyName = [
                    { required: true, message: '单位名称不能为空', trigger: 'change' }
                ];
            }

            // 根据消息类型添加相应的验证规则
            if (this.dataForm.type === 2) {
                rules.title = [
                    { required: true, message: '消息标题不能为空', trigger: 'blur' }
                ];
                rules.picUrl = [
                    { required: true, message: '封面不能为空', trigger: 'blur' }
                ];
                rules.linkUrl = [
                    { required: true, message: '链接不能为空', trigger: 'blur' }
                ];
            }

            return rules;
        },
        // 动态审核验证规则
        dynamicApproveRules() {
            const rules = {
                status: [
                    { required: true, message: '审核结果不能为空', trigger: 'change' }
                ]
            };

            // 当审核状态为不通过(2)时，审核意见必填
            if (this.approveForm.status === 2) {
                rules.reviewNote = [
                    { required: true, message: '审核意见不能为空', trigger: 'blur' }
                ];
            }

            return rules;
        }
    },
    mounted() {
        this.getList();
        this.loadOptions();
    },
    methods: {
        // 加载选项数据
        async loadOptions() {
            try {
                // 加载行业选项
                const industryRes = await getAllAttList(1);
                this.industryOptions = industryRes || []

                // 加载部门选项
                const departmentRes = await getAllAttList(2);
                this.departmentOptions = departmentRes || []

                // 加载属地选项
                const territoryRes = await getAllAttList(3);
                this.territoryOptions = territoryRes || []

                // 加载单位选项
                const companyRes = await getAllList();
                this.companyOptions = companyRes || [];
            } catch (error) {
                console.error('加载选项数据失败:', error);
            }
        },

        // 推送范围变化处理
        handlePushScopeChange() {
            // 清空相关字段
            this.dataForm.industry = "";
            this.dataForm.competent = "";
            this.dataForm.dependency = "";
            this.dataForm.companyName = "";
        },

        // 图片上传处理
        handlePicUpload(file) {
            // 这里应该调用实际的图片上传接口
            // 暂时使用本地预览
            const reader = new FileReader();
            reader.onload = (e) => {
                this.dataForm.picUrl = e.target.result;
            };
            reader.readAsDataURL(file.raw);
        },

        submitForm() {
            if (this.isView) {
                this.dialogVisible = false;
                return;
            }

            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    try {
                        if (this.isChange) {
                            let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                            dataForm.id = this.editId;
                            let res = await updatePolicy(dataForm);
                            if (res.code === 200) {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                });
                                this.getList();
                                this.dialogVisible = false;
                            }
                        } else {
                            let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                            let res = await addPolicy(dataForm);
                            if (res.code === 200) {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                });
                                this.getList();
                                this.dialogVisible = false;
                            }
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        this.$message({
                            message: '操作失败',
                            type: 'error'
                        });
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.isView = false;
            this.title = "新建消息";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm));
            });
        },

        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm));
            this.dialogVisible = false;
            this.isView = false;
        },

        // 查看
        async handleView(val) {
            this.title = "查看消息";
            this.isView = true;
            this.dataForm = { ...val };
            this.editId = val.id;
            this.dialogVisible = true;
            this.isChange = false;
        },

        // 编辑
        async handleEdit(val) {
            this.title = "修改消息";
            this.isView = false;
            this.dataForm = { ...val };
            this.editId = val.id;
            this.dialogVisible = true;
            this.isChange = true;
        },

        // 删除
        handleDelete(val) {
            this.$confirm('确认删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    let res = await delPolicy({ ids: [val.id] });
                    if (res.code === 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList();
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    this.$message({
                        message: '删除失败',
                        type: 'error'
                    });
                }
            })
        },

        // 审核
        handleApprove(val) {
            this.editId = val.id;
            this.approveForm = {
                status: "",
                reviewNote: ""
            };
            this.approveDialogVisible = true;
        },

        // 提交审核
        submitApprove() {
            this.$refs.approveForm.validate(async (valid) => {
                if (valid) {
                    try {
                        let approveData = {
                            id: this.editId,
                            ...this.approveForm
                        };
                        let res = await checkPolicy(approveData);
                        if (res.code === 200) {
                            this.$message({
                                message: '审核成功',
                                type: 'success'
                            });
                            this.getList();
                            this.approveDialogVisible = false;
                        }
                    } catch (error) {
                        console.error('审核失败:', error);
                        this.$message({
                            message: '审核失败',
                            type: 'error'
                        });
                    }
                }
            });
        },

        // 发送
        handleSend(val) {
            this.$confirm('确认发送该消息吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    let res = await sendPolicy(val.id);
                    if (res.code === 200) {
                        this.$message({
                            message: '发送成功',
                            type: 'success'
                        });
                        this.getList();
                    }
                } catch (error) {
                    console.error('发送失败:', error);
                    this.$message({
                        message: '发送失败',
                        type: 'error'
                    });
                }
            }).catch(() => {
                // 用户取消发送
            });
        },
        // 重置搜索
        reset() {
            this.queryParams = {
                status: '',
                type: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList();
        },

        // 获取列表
        async getList() {
            this.loading = true;
            try {
                let res = await getPolicyList(this.queryParams);
                if (res.code === 200) {
                    this.total = res.total;
                    this.list = res.rows;
                }
            } catch (error) {
                console.error('获取列表失败:', error);
                this.$message({
                    message: '获取列表失败',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
