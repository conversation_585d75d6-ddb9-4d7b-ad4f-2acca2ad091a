# 用户ID解析功能测试

## 功能说明
实现了编辑消息时根据 `userIds` 字段回显用户名的功能。

## 支持的数据格式

### 输入格式
- 字符串格式：`"[6,8]"` 或 `"6,8"` 或 `"[6, 8]"`
- 数组格式：`[6, 8]`

### 解析逻辑
1. 检测数据类型（字符串或数组）
2. 如果是字符串，移除方括号并按逗号分割
3. 转换为整数数组
4. 调用用户列表API获取所有用户
5. 根据 `wechatId` 匹配用户信息

## 测试步骤

### 1. 编辑现有消息测试
```javascript
// 模拟数据结构
const messageData = {
  id: 1,
  title: "测试消息",
  content: "测试内容",
  userIds: "[6,8]"  // 关键字段
}
```

1. 在消息列表中点击"编辑"按钮
2. 检查控制台输出：
   ```
   解析用户ID: [6, 8]
   匹配到的用户: [{wechatId: 6, name: "张三", ...}, {wechatId: 8, name: "李四", ...}]
   ```
3. 验证消息对话框中显示已选用户名
4. 点击"选择用户"按钮，验证用户选择框中对应用户已被选中

### 2. 查看消息测试
1. 在消息列表中点击"查看"按钮
2. 验证已选用户正确显示
3. 用户选择按钮应该被禁用（查看模式）

### 3. 提交数据测试
1. 编辑消息并修改用户选择
2. 提交表单
3. 检查提交的数据格式：
   ```javascript
   {
     userList: [6, 8],      // 内部使用的数组格式
     userIds: "[6,8]"       // 提交给后端的字符串格式
   }
   ```

## 调试功能

### 控制台输出
- `解析用户ID:` - 显示解析后的用户ID数组
- `匹配到的用户:` - 显示匹配到的用户详细信息
- `用户列表数据:` - 显示API返回的所有用户数据

### 界面调试信息
- 筛选结果统计
- 已选用户名称列表
- 用户数量统计

## 错误处理

### 常见问题
1. **用户ID不存在**：显示 "用户X" 作为默认名称
2. **API调用失败**：控制台显示错误信息
3. **数据格式错误**：自动尝试解析，失败时忽略

### 容错机制
- 支持多种输入格式
- 无效ID自动过滤
- 网络错误时显示默认信息
- 空数据时不执行解析

## 注意事项
1. 确保 `wechatId` 字段作为用户唯一标识
2. API返回的用户列表应包含所有用户（pageSize: 999）
3. 生产环境记得移除调试信息
4. 测试各种边界情况（空数据、格式错误等）
